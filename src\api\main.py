"""
FastAPI主应用文件

遥感图像分类系统的API服务入口
"""

import logging

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn

from .config import settings
from .models.common import HealthResponse, APIInfoResponse

# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL), format=settings.LOG_FORMAT
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.APP_NAME,
    description=settings.APP_DESCRIPTION,
    version=settings.APP_VERSION,
    docs_url="/docs",
    redoc_url="/redoc",
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS if not settings.DEBUG else ["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置静态文件服务
if settings.STATIC_DIR.exists():
    app.mount("/static", StaticFiles(directory=str(settings.STATIC_DIR)), name="static")
    logger.info(f"静态文件服务已配置: {settings.STATIC_DIR}")
else:
    logger.warning(f"静态文件目录不存在: {settings.STATIC_DIR}")


# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"全局异常: {exc}")
    return {"error": "Internal Server Error", "message": "内部服务器错误"}


# 健康检查端点
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    return HealthResponse(status="healthy", message="遥感图像分类API服务正常运行")


# 根路径端点
@app.get("/", response_class=HTMLResponse)
async def root():
    """根路径，返回主页面"""
    index_file = settings.STATIC_DIR / "index.html"

    if index_file.exists():
        return HTMLResponse(content=index_file.read_text(encoding="utf-8"))
    else:
        return HTMLResponse(
            content="""
        <html>
            <head><title>遥感图像分类系统</title></head>
            <body>
                <h1>遥感图像分类API</h1>
                <p>API文档: <a href="/docs">/docs</a></p>
                <p>健康检查: <a href="/health">/health</a></p>
            </body>
        </html>
        """
        )


# API信息端点
@app.get("/api/info", response_model=APIInfoResponse)
async def api_info():
    """获取API基本信息"""
    return APIInfoResponse(
        name=settings.APP_NAME,
        version=settings.APP_VERSION,
        description=settings.APP_DESCRIPTION,
        endpoints={
            "health": "/health",
            "docs": "/docs",
            "prediction": "/api/predict",
            "models": "/api/models",
            "adaptive": "/api/adaptive",
            "dataset": "/api/dataset",
        },
    )


# 导入路由模块
from .routers import prediction

app.include_router(prediction.router, prefix="/api", tags=["prediction"])

# 其他路由模块（稍后会创建）
# from .routers import models, adaptive, dataset
# app.include_router(models.router, prefix="/api", tags=["models"])
# app.include_router(adaptive.router, prefix="/api", tags=["adaptive"])
# app.include_router(dataset.router, prefix="/api", tags=["dataset"])

if __name__ == "__main__":
    # 开发环境启动配置
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True, log_level="info")
