"""
预测API测试脚本

测试预测API的基本功能
"""

import sys
import os
sys.path.append('src')

from fastapi.testclient import TestClient
from api.main import app

# 创建测试客户端
client = TestClient(app)

def test_health_check():
    """测试健康检查端点"""
    response = client.get("/health")
    print(f"健康检查状态码: {response.status_code}")
    print(f"健康检查响应: {response.json()}")
    return response.status_code == 200

def test_api_info():
    """测试API信息端点"""
    response = client.get("/api/info")
    print(f"API信息状态码: {response.status_code}")
    print(f"API信息响应: {response.json()}")
    return response.status_code == 200

def test_get_models():
    """测试获取模型列表"""
    response = client.get("/api/models")
    print(f"模型列表状态码: {response.status_code}")
    print(f"模型列表响应: {response.json()}")
    return response.status_code == 200

def test_get_classes():
    """测试获取类别列表"""
    response = client.get("/api/classes")
    print(f"类别列表状态码: {response.status_code}")
    print(f"类别列表响应: {response.json()}")
    return response.status_code == 200

if __name__ == "__main__":
    print("开始测试预测API...")
    
    tests = [
        ("健康检查", test_health_check),
        ("API信息", test_api_info),
        ("模型列表", test_get_models),
        ("类别列表", test_get_classes),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n=== 测试 {test_name} ===")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"✅ {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"❌ {test_name}: 异常 - {e}")
    
    print(f"\n=== 测试总结 ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
